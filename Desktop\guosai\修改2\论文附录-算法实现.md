# 附录：核心算法实现

## A.1 问题1核心算法

### A.1.1 波长依赖折射率模型

基于<PERSON><PERSON><PERSON><PERSON>方程和Drude模型的组合：

```python
class RefractiveIndexModel:
    def __init__(self, material='SiC'):
        if material == 'SiC':
            self.A, self.B1, self.C1 = 6.7, 1.73, 0.256
            self.B2, self.C2 = 0.32, 1250.0
            self.m_eff, self.gamma = 0.67 * m_e, 1e13
  
    def intrinsic_refractive_index(self, wavelength_um):
        """<PERSON><PERSON><PERSON><PERSON>方程：n₀²(λ) = A + B₁λ²/(λ²-C₁) + B₂λ²/(λ²-C₂)"""
        lambda_sq = wavelength_um**2
        n_sq = self.A + (self.B1 * lambda_sq) / (lambda_sq - self.C1) + \
               (self.B2 * lambda_sq) / (lambda_sq - self.C2)
        return np.sqrt(np.maximum(n_sq, 1.0))
  
    def refractive_index_with_carriers(self, wavelength_um, carrier_concentration):
        """Drude模型：n(λ,N) = √[n₀²(λ) - ωₚ²(N)/(ω² + γ²)]"""
        n0 = self.intrinsic_refractive_index(wavelength_um)
        if carrier_concentration <= 0:
            return n0
      
        omega = 2 * np.pi * c / (wavelength_um * 1e-6)
        omega_p = np.sqrt(carrier_concentration * 1e6 * e**2 / (epsilon_0 * self.m_eff))
      
        epsilon_inf = n0**2
        epsilon_carrier = -omega_p**2 / (omega**2 + 1j*self.gamma*omega)
        epsilon_total = epsilon_inf + epsilon_carrier
      
        return np.sqrt(np.real(epsilon_total))
```

### A.1.2 载流子浓度估计算法

```python
def estimate_carrier_concentration(wavenumber, reflectance, material='SiC'):
    """基于长波段光谱斜率估计载流子浓度"""
    long_wave_mask = wavenumber >= 800
    if np.sum(long_wave_mask) < 5:
        return 1e16 if material == 'SiC' else 1e15
  
    wn_long = wavenumber[long_wave_mask]
    ref_long = reflectance[long_wave_mask]
    slope = np.polyfit(wn_long, ref_long, 1)[0]
  
    if material == 'SiC':
        if slope < -0.008: return 5e16
        elif slope < -0.004: return 2e16
        else: return 8e15
    else:  # Si
        if slope < -0.005: return 1e16
        elif slope < -0.002: return 5e15
        else: return 1e15
```

## A.2 问题2核心算法

### A.2.1 FFT光程差分析算法

```python
def calculate_opd_from_fft(uniform_wavenumber, uniform_reflectance):
    """FFT分析提取光程差"""
    N = len(uniform_wavenumber)
    wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
  
    # 基线校正并执行FFT
    reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
    reflectance_fft = fft(reflectance_centered)
    fft_magnitude = np.abs(reflectance_fft)
  
    # 计算光程差轴
    opd_axis = fftfreq(N, d=wavenumber_step)
    positive_opd_axis = opd_axis[:N // 2]
    positive_fft_magnitude = fft_magnitude[:N // 2]
  
    # 主峰识别
    start_idx = max(1, int(0.001 * N))
    peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
    opd_value = positive_opd_axis[peak_index]
  
    return opd_value, positive_opd_axis, positive_fft_magnitude
```

### A.2.2 厚度计算算法

```python
def calculate_thickness_traditional(opd, n1, theta_i_deg):
    """传统方法：d = L / (2 × √(n₁² - sin²(θᵢ)))"""
    theta_i_rad = np.deg2rad(theta_i_deg)
    denominator = 2 * np.sqrt(n1**2 - np.sin(theta_i_rad)**2)
    thickness_cm = opd / denominator
    return thickness_cm * 1e4  # 转换为微米

def calculate_thickness_optimized(wavenumber, reflectance, opd, theta_i_deg, material='SiC'):
    """优化方法：考虑波长依赖折射率和载流子效应"""
    # 1. 估计载流子浓度
    N = estimate_carrier_concentration(wavenumber, reflectance, material)
  
    # 2. 计算波长依赖折射率
    ri_model = RefractiveIndexModel(material)
    analysis_mask = (wavenumber >= 500) & (wavenumber <= 1500)
    wavelength_um = 1e4 / wavenumber[analysis_mask]
    n_wavelength = ri_model.refractive_index_with_carriers(wavelength_um, N)
  
    # 3. 加权平均折射率
    ref_analysis = reflectance[analysis_mask]
    weights = np.abs(ref_analysis - np.mean(ref_analysis))
    weights = weights / np.sum(weights) if np.sum(weights) > 0 else np.ones_like(weights) / len(weights)
    n_avg_optimized = np.average(n_wavelength, weights=weights)
  
    # 4. 计算厚度
    theta_i_rad = np.deg2rad(theta_i_deg)
    denominator = 2 * np.sqrt(n_avg_optimized**2 - np.sin(theta_i_rad)**2)
    thickness_um = opd / denominator * 1e4
  
    return {
        'thickness_um': thickness_um,
        'carrier_concentration': N,
        'optimized_n_avg': n_avg_optimized
    }
```

### A.2.3 可靠性分析算法

```python
def analyze_reliability(thickness_10, thickness_15):
    """双角度可靠性分析"""
    avg_thickness = (thickness_10 + thickness_15) / 2
    relative_error = abs(thickness_10 - thickness_15) / avg_thickness * 100
  
    if relative_error < 1.0: reliability = "优秀"
    elif relative_error < 5.0: reliability = "良好"
    elif relative_error < 10.0: reliability = "可接受"
    else: reliability = "需改进"
  
    return {
        'average': avg_thickness,
        'relative_error': relative_error,
        'reliability': reliability
    }
```

## A.3 问题3核心算法

### A.3.1 多光束干涉条件判断

```python
def analyze_multibeam_interference_condition(n1, n2, thickness_um, wavelength_um):
    """多光束干涉必要条件分析"""
    # 计算反射系数
    r1 = (1 - n1) / (1 + n1)  # 空气-外延层界面
    r2 = (n1 - n2) / (n1 + n2)  # 外延层-衬底界面
  
    # 计算精细度
    finesse = 4 * r1 * r2 / (1 - r1 * r2)**2
  
    # 判断多光束干涉条件
    multibeam_threshold = 0.1
    is_multibeam = finesse > multibeam_threshold
  
    return {
        'finesse': finesse,
        'is_multibeam': is_multibeam,
        'r1': r1,
        'r2': r2
    }
```

### A.3.2 多材料支持算法

```python
class MultiMaterialCalculator:
    def __init__(self, material='SiC'):
        self.material = material
        self.ri_model = RefractiveIndexModel(material)
  
    def calculate_thickness_optimized(self, wavenumber, reflectance, angle_deg):
        """多材料优化厚度计算"""
        # 数据预处理
        uniform_wn, uniform_ref = self._preprocess_data(wavenumber, reflectance)
      
        # FFT分析
        opd, _, _ = calculate_opd_from_fft(uniform_wn, uniform_ref)
      
        # 载流子浓度估计
        N = estimate_carrier_concentration(wavenumber, reflectance, self.material)
      
        # 优化折射率计算
        wavelength_um = 1e4 / uniform_wn
        n_optimized = self.ri_model.refractive_index_with_carriers(wavelength_um, N)
        n_avg = np.mean(n_optimized)
      
        # 厚度计算
        angle_rad = np.deg2rad(angle_deg)
        thickness_um = opd / (2 * np.sqrt(n_avg**2 - np.sin(angle_rad)**2)) * 1e4
      
        return {
            'thickness_um': thickness_um,
            'carrier_concentration': N,
            'average_refractive_index': n_avg,
            'material': self.material
        }
```

## A.4 数据处理辅助算法

### A.4.1 数据加载与预处理

```python
def load_spectral_data(file_path):
    """多编码格式数据加载"""
    encodings = ['gbk', 'gb2312', 'utf-8', 'latin-1']
    for encoding in encodings:
        try:
            if file_path.endswith('.xlsx'):
                df = pd.read_excel(file_path)
            else:
                df = pd.read_csv(file_path, encoding=encoding)
          
            wavenumber = df.iloc[:, 0].to_numpy()
            reflectance = df.iloc[:, 1].to_numpy()
          
            # 清理无效数据
            valid_mask = ~(np.isnan(wavenumber) | np.isnan(reflectance))
            return wavenumber[valid_mask], reflectance[valid_mask]
        except:
            continue
    raise ValueError(f"无法读取文件 {file_path}")

def preprocess_and_interpolate(wavenumber, reflectance, num_points=2**16):
    """线性插值到均匀网格"""
    interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                          bounds_error=False, fill_value='extrapolate')
    uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
    uniform_reflectance = interp_func(uniform_wavenumber)
    return uniform_wavenumber, uniform_reflectance
```

## A.5 算法使用流程

### A.5.1 基本使用流程

```python
# 1. 数据加载
wavenumber, reflectance = load_spectral_data('附件1.csv')

# 2. 数据预处理
uniform_wn, uniform_ref = preprocess_and_interpolate(wavenumber, reflectance)

# 3. FFT分析
opd, _, _ = calculate_opd_from_fft(uniform_wn, uniform_ref)

# 4. 厚度计算
thickness_traditional = calculate_thickness_traditional(opd, 2.58, 10.0)
result_optimized = calculate_thickness_optimized(wavenumber, reflectance, opd, 10.0, 'SiC')

# 5. 可靠性分析
reliability = analyze_reliability(thickness_10deg, thickness_15deg)
```

### A.5.2 关键参数设置

| 参数类型     | SiC材料   | Si材料    | 说明            |
| ------------ | --------- | --------- | --------------- |
| 折射率范围   | 2.5-2.7   | 3.3-3.5   | 典型值          |
| 载流子浓度   | 1e15-1e17 | 1e14-1e16 | cm⁻³          |
| FFT点数      | 2¹⁶     | 2¹⁶     | 必须为2的幂次方 |
| 分析波数范围 | 400-1200  | 400-1200  | cm⁻¹          |

**注**：完整算法实现见 `core_algorithms.py` 文件，本附录仅展示核心代码片段。
