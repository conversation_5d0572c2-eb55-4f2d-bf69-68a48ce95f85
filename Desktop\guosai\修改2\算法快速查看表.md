# 碳化硅外延层厚度测定算法快速查看表

## 📊 算法总览表

| 问题 | 核心算法 | 主要创新点 | 文件位置 | 行数范围 |
|------|----------|------------|----------|----------|
| **问题1** | 优化折射率模型 | <PERSON><PERSON><PERSON><PERSON> + <PERSON><PERSON>模型 | `problem1_solution.py` | 41-140 |
| **问题2** | FFT厚度计算 | 数值优化 + 消光系数 | `problem2_solution.py` | 56-291 |
| **问题3** | 多光束干涉分析 | 多材料支持框架 | `problem3_solution.py` | 39-325 |

---

## 🔧 核心函数速查表

### 数据处理类函数
| 函数名 | 功能 | 输入参数 | 输出结果 | 代码位置 |
|--------|------|----------|----------|----------|
| `load_spectral_data()` | 加载光谱数据 | 文件路径 | 波数, 反射率数组 | `core_algorithms.py:95-110` |
| `preprocess_and_interpolate()` | 数据预处理插值 | 波数, 反射率, 点数 | 均匀数据网格 | `core_algorithms.py:112-118` |

### FFT分析类函数
| 函数名 | 功能 | 输入参数 | 输出结果 | 代码位置 |
|--------|------|----------|----------|----------|
| `calculate_opd_from_fft()` | FFT光程差分析 | 均匀波数, 反射率 | 光程差, 频谱 | `core_algorithms.py:121-138` |

### 厚度计算类函数
| 函数名 | 功能 | 输入参数 | 输出结果 | 代码位置 |
|--------|------|----------|----------|----------|
| `calculate_thickness_traditional()` | 传统厚度计算 | 光程差, 折射率, 角度 | 厚度(μm) | `core_algorithms.py:141-147` |
| `calculate_thickness_optimized()` | 优化厚度计算 | 光谱数据, 角度, 材料 | 结果字典 | `core_algorithms.py:149-185` |

### 分析评估类函数
| 函数名 | 功能 | 输入参数 | 输出结果 | 代码位置 |
|--------|------|----------|----------|----------|
| `analyze_reliability()` | 可靠性分析 | 两个厚度值 | 分析结果字典 | `core_algorithms.py:188-208` |
| `analyze_multibeam_interference_condition()` | 多光束干涉分析 | 折射率, 厚度, 波长 | 干涉条件判断 | `core_algorithms.py:211-225` |

---

## 🏗️ 核心模型类速查表

### 折射率模型类
| 类名 | 功能 | 关键方法 | 数学模型 | 代码位置 |
|------|------|----------|----------|----------|
| `RefractiveIndexModel` | 波长依赖折射率 | `refractive_index_with_carriers()` | n(λ,N) = √[n₀²(λ) - ωₚ²/(ω²+γ²)] | `core_algorithms.py:18-75` |
| `CarrierConcentrationEstimator` | 载流子浓度估计 | `estimate_from_spectrum_shape()` | 基于光谱斜率分析 | `core_algorithms.py:78-92` |
| `ExtinctionCoefficientModel` | 消光系数模型 | `calculate_extinction()` | κ = A × λᵖ | `core_algorithms.py:228-238` |

---

## 📋 问题专用算法对照表

### 问题1：双光束干涉优化模型
| 算法组件 | 具体实现 | 核心公式 | 文件位置 |
|----------|----------|----------|----------|
| **Sellmeier方程** | `intrinsic_refractive_index()` | n₀²(λ) = A + B₁λ²/(λ²-C₁) + B₂λ²/(λ²-C₂) | `problem1_solution.py:82-90` |
| **Drude模型** | `refractive_index_with_carriers()` | ωₚ = √(Ne²/(ε₀m*)) | `problem1_solution.py:103-128` |
| **载流子估计** | `estimate_from_spectrum_shape()` | 基于长波段斜率 | `problem1_solution.py:152-189` |
| **优化厚度计算** | `calculate_thickness_optimized()` | d = L/(2√(n²-sin²θ)) | `problem1_solution.py:354-427` |

### 问题2：FFT算法设计
| 算法组件 | 具体实现 | 核心原理 | 文件位置 |
|----------|----------|----------|----------|
| **数据预处理** | `preprocess_data()` | 线性插值到均匀网格 | `problem2_solution.py:343-370` |
| **FFT分析** | `analyze_fft()` | 波数域→光程差域变换 | `problem2_solution.py:373-416` |
| **厚度计算** | `calculate_thickness()` | L = 2d√(n₁²-sin²θᵢ) | `problem2_solution.py:419-451` |
| **可靠性分析** | `analyze_reliability()` | 双角度交叉验证 | `problem2_solution.py:454-522` |
| **数值优化** | `OptimizedThicknessCalculator` | 差分进化算法 | `problem2_solution.py:56-291` |

### 问题3：多光束干涉分析
| 算法组件 | 具体实现 | 核心理论 | 文件位置 |
|----------|----------|----------|----------|
| **多材料模型** | `MultiMaterialRefractiveIndexModel` | SiC/Si参数分离 | `problem3_solution.py:39-117` |
| **载流子估计** | `MaterialCarrierEstimator` | 材料相关估计策略 | `problem3_solution.py:119-168` |
| **多光束条件** | `analyze_multibeam_interference_condition()` | 精细度F = 4r₁r₂/(1-r₁r₂)² | `core_algorithms.py:211-225` |
| **优化计算器** | `OptimizedMultiMaterialCalculator` | 迭代优化算法 | `problem3_solution.py:171-325` |

---

## ⚡ 快速使用代码片段

### 基础使用流程
```python
# 1. 导入核心算法
from core_algorithms import *

# 2. 加载数据
wavenumber, reflectance = load_spectral_data('附件1.csv')

# 3. FFT分析
uniform_wn, uniform_ref = preprocess_and_interpolate(wavenumber, reflectance)
opd, _, _ = calculate_opd_from_fft(uniform_wn, uniform_ref)

# 4. 计算厚度
thickness = calculate_thickness_traditional(opd, 2.58, 10.0)
```

### 问题1优化方法
```python
# 优化折射率模型
result = calculate_thickness_optimized(wavenumber, reflectance, opd, 10.0, 'SiC')
print(f"优化厚度: {result['thickness_um']:.3f} μm")
```

### 问题2可靠性分析
```python
# 双角度可靠性验证
reliability = analyze_reliability(thickness_10deg, thickness_15deg)
print(f"相对误差: {reliability['relative_error']:.2f}%")
```

### 问题3多材料分析
```python
# 多光束干涉条件判断
condition = analyze_multibeam_interference_condition(2.6, 3.2, 30.0, 12.0)
print(f"多光束干涉: {'是' if condition['is_multibeam'] else '否'}")
```

---

## 🎯 参数快速设置表

### 材料参数表
| 材料 | 折射率 | 载流子浓度 | Sellmeier参数A | 有效质量 |
|------|--------|------------|----------------|----------|
| **SiC** | 2.5-2.7 | 1e15-1e17 | 6.7 | 0.67×mₑ |
| **Si** | 3.3-3.5 | 1e14-1e16 | 11.7 | 0.26×mₑ |

### FFT参数表
| 参数 | 推荐值 | 说明 |
|------|--------|------|
| **FFT点数** | 2¹⁶ (65536) | 必须为2的幂次方 |
| **分析范围** | 400-1200 cm⁻¹ | 主要干涉区域 |
| **插值步长** | 0.5 cm⁻¹ | 均匀网格间距 |

### 优化边界表
| 参数 | 下界 | 上界 | 单位 |
|------|------|------|------|
| **厚度** | FFT估计×0.8 | FFT估计×1.2 | μm |
| **消光系数A** | 1e-5 | 1e-3 | - |
| **消光系数p** | 0.5 | 2.0 | - |

---

## 🔍 故障诊断速查表

| 问题现象 | 可能原因 | 解决方案 | 相关函数 |
|----------|----------|----------|----------|
| **数据加载失败** | 编码问题 | 检查文件编码格式 | `load_spectral_data()` |
| **FFT峰值异常** | 噪声干扰 | 调整分析波数范围 | `calculate_opd_from_fft()` |
| **厚度结果偏大** | 折射率过小 | 检查材料参数设置 | `RefractiveIndexModel` |
| **可靠性较差** | 数据质量差 | 提高光谱数据质量 | `analyze_reliability()` |
| **优化不收敛** | 边界设置错误 | 调整参数边界范围 | `numerical_optimization_thickness()` |

---

## 📈 性能指标速查表

| 指标类型 | 优秀 | 良好 | 可接受 | 需改进 |
|----------|------|------|--------|--------|
| **相对误差** | <1% | 1-5% | 5-10% | >10% |
| **R²拟合度** | >0.99 | 0.95-0.99 | 0.90-0.95 | <0.90 |
| **载流子浓度** | 1e15-1e17 | 合理范围 | 需验证 | 异常值 |

---

## 📚 文件结构速查表

| 文件名 | 主要内容 | 使用场景 | 文件大小 |
|--------|----------|----------|----------|
| `core_algorithms.py` | 核心算法实现 | 算法调用 | ~300行 |
| `算法附录.md` | 详细算法说明 | 论文附录 | ~300行 |
| `算法使用示例.py` | 使用示例代码 | 学习参考 | ~300行 |
| `算法快速参考.md` | 快速参考手册 | 日常查阅 | ~300行 |
| `算法快速查看表.md` | 本文件 | 快速定位 | ~200行 |

---

**💡 使用提示**: 
- 按 `Ctrl+F` 搜索关键词快速定位
- 建议收藏本表格便于日常查阅
- 结合具体代码文件使用效果更佳
