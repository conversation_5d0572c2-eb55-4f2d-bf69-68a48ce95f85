# 算法快速参考卡

## 🚀 快速开始

### 基本流程
```python
# 1. 导入算法库
from core_algorithms import *

# 2. 加载数据
wavenumber, reflectance = load_spectral_data('data.csv')

# 3. 预处理
uniform_wn, uniform_ref = preprocess_and_interpolate(wavenumber, reflectance)

# 4. FFT分析
opd, opd_axis, fft_mag = calculate_opd_from_fft(uniform_wn, uniform_ref)

# 5. 计算厚度
thickness = calculate_thickness_traditional(opd, 2.58, 10.0)
```

---

## 📋 核心函数速查

### 数据处理
| 函数 | 功能 | 输入 | 输出 |
|------|------|------|------|
| `load_spectral_data(file_path)` | 加载光谱数据 | 文件路径 | 波数, 反射率 |
| `preprocess_and_interpolate(wn, ref)` | 数据预处理 | 原始数据 | 均匀数据 |

### 分析计算
| 函数 | 功能 | 输入 | 输出 |
|------|------|------|------|
| `calculate_opd_from_fft(wn, ref)` | FFT光程差分析 | 均匀数据 | 光程差, 频谱 |
| `calculate_thickness_traditional(opd, n, θ)` | 传统厚度计算 | 光程差, 折射率, 角度 | 厚度(μm) |
| `calculate_thickness_optimized(...)` | 优化厚度计算 | 光谱数据, 参数 | 结果字典 |
| `analyze_reliability(t1, t2)` | 可靠性分析 | 两个厚度值 | 分析结果 |

### 模型类
| 类 | 功能 | 主要方法 |
|----|------|----------|
| `RefractiveIndexModel(material)` | 折射率模型 | `refractive_index_with_carriers()` |
| `CarrierConcentrationEstimator(material)` | 载流子估计 | `estimate_from_spectrum_shape()` |
| `ExtinctionCoefficientModel()` | 消光系数模型 | `calculate_extinction()` |

---

## 🎯 问题专用算法

### 问题1：优化折射率模型
```python
# 创建折射率模型
ri_model = RefractiveIndexModel('SiC')

# 估计载流子浓度
estimator = CarrierConcentrationEstimator('SiC')
N = estimator.estimate_from_spectrum_shape(wavenumber, reflectance)

# 计算波长依赖折射率
wavelength = 1e4 / wavenumber  # 波数转波长
n = ri_model.refractive_index_with_carriers(wavelength, N)

# 优化厚度计算
result = calculate_thickness_optimized(wavenumber, reflectance, opd, 10.0, 'SiC')
```

### 问题2：FFT算法设计
```python
# 完整的FFT厚度计算流程
def fft_thickness_calculation(file_path, angle_deg):
    # 1. 数据加载
    wn, ref = load_spectral_data(file_path)
    
    # 2. 预处理
    uniform_wn, uniform_ref = preprocess_and_interpolate(wn, ref)
    
    # 3. FFT分析
    opd, _, _ = calculate_opd_from_fft(uniform_wn, uniform_ref)
    
    # 4. 厚度计算
    thickness = calculate_thickness_traditional(opd, 2.58, angle_deg)
    
    return thickness
```

### 问题3：多光束干涉分析
```python
# 多光束干涉条件判断
result = analyze_multibeam_interference_condition(n1=2.6, n2=3.2, 
                                                thickness_um=30.0, 
                                                wavelength_um=12.0)

# 多材料支持
sic_model = RefractiveIndexModel('SiC')
si_model = RefractiveIndexModel('Si')

# 数值优化
bounds = [(20.0, 30.0), (1e-5, 1e-3), (0.5, 2.0)]
opt_result = numerical_optimization_thickness(wavenumber, reflectance, 10.0, bounds)
```

---

## ⚙️ 参数设置指南

### 材料参数
| 材料 | 折射率范围 | 载流子浓度范围 | 推荐设置 |
|------|------------|----------------|----------|
| SiC | 2.5-2.7 | 1e15-1e17 cm⁻³ | `material='SiC'` |
| Si | 3.3-3.5 | 1e14-1e16 cm⁻³ | `material='Si'` |

### FFT参数
| 参数 | 推荐值 | 说明 |
|------|--------|------|
| `num_points` | 2^16 (65536) | FFT点数，2的幂次方 |
| 分析范围 | 400-1200 cm⁻¹ | 主要干涉区域 |
| 插值步长 | 0.5 cm⁻¹ | 均匀网格步长 |

### 优化参数
| 参数 | 范围 | 说明 |
|------|------|------|
| 厚度边界 | ±20% FFT估计值 | 基于FFT结果设置 |
| 消光系数A | 1e-5 ~ 1e-3 | 材料相关 |
| 消光系数p | 0.5 ~ 2.0 | 波长依赖指数 |

---

## 🔍 故障排除

### 常见问题及解决方案

| 问题 | 可能原因 | 解决方案 |
|------|----------|----------|
| 数据加载失败 | 编码格式问题 | 检查文件编码，使用UTF-8 |
| FFT峰值错误 | 噪声干扰 | 调整分析波数范围 |
| 厚度结果异常 | 折射率参数错误 | 检查材料类型设置 |
| 可靠性较差 | 数据质量问题 | 检查原始光谱质量 |
| 优化不收敛 | 边界设置不当 | 调整参数边界范围 |

### 精度提升技巧
1. **使用优化方法**：`calculate_thickness_optimized()` 比传统方法精度更高
2. **双角度验证**：使用10°和15°入射角交叉验证
3. **材料匹配**：确保材料参数与实际样品匹配
4. **数据质量**：使用高质量的光谱数据
5. **参数调优**：根据具体情况调整算法参数

---

## 📊 性能指标

### 精度指标
- **相对误差 < 1%**：优秀
- **相对误差 < 5%**：良好  
- **相对误差 < 10%**：可接受

### 适用范围
- **厚度范围**：1-100 μm
- **波数范围**：400-1200 cm⁻¹
- **材料类型**：SiC, Si
- **入射角**：5°-20°

### 算法复杂度
- **FFT分析**：O(N log N)
- **优化算法**：O(N × 迭代次数)
- **内存需求**：~100MB (典型数据)

---

## 📚 扩展阅读

### 理论基础
- Sellmeier方程：本征折射率计算
- Drude模型：载流子效应修正
- Fabry-Perot干涉：多光束干涉理论
- FFT算法：频域分析原理

### 实际应用
- 半导体器件制造
- 材料表征分析
- 质量控制检测
- 科研实验测量

---

**💡 提示**: 建议先运行 `算法使用示例.py` 熟悉各个算法的使用方法，然后根据实际需求调用相应的函数。
