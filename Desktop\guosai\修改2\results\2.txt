======================================================================
问题2：确定外延层厚度的算法设计与实现（优化版本）
基于波长依赖折射率模型和载流子效应修正的FFT算法方案
======================================================================

==================================================
第一步：算法参数设置
==================================================
数据文件：
  附件1：data/附件1.csv (10°入射角)
  附件2：data/附件2.csv (15°入射角)
算法特点：
  - 波长依赖的折射率模型（Sellmeier方程）
  - 自由载流子效应修正（Drude模型）
  - 载流子浓度自动估计
  - 迭代优化算法

==================================================
第二步：处理附件1数据 (10°入射角)
==================================================
✓ 成功读取文件：附件1.csv (编码: gbk)
  数据点数量：7469
  波数范围：399.67 - 4000.12 cm^-1
  反射率范围：0.00 - 95.38 %
执行传统方法分析...
  执行数据预处理...
  ✓ 插值完成：65536 个均匀数据点
  ✓ 波数步长：0.0549 cm^-1
  执行FFT分析...
  ✓ FFT分析完成
  ✓ 检测到主峰光程差：0.018331 cm
  ✓ 主峰幅度：13821.69
  计算外延层厚度...
  ✓ 入射角：10.0°
  ✓ 光程差：0.018331 cm
  ✓ 折射率：2.58
  ✓ 计算厚度：35.605 μm
执行数值优化分析...
  执行数值优化分析...
  初始光程差: 0.018331 cm
  估计厚度范围: 28.3 - 42.4 μm
    使用差分进化算法优化(d, A, p)参数...
  ✓ 优化完成:
    最优厚度: 28.545 μm
    消光系数参数A: 1.813198e-03
    消光系数参数p: 3.0000
    拟合R²: -1.261228
✓ 10°入射角处理完成
  传统方法厚度：35.605 μm
  优化方法厚度：28.545 μm
  消光系数参数A：1.813198e-03
  消光系数参数p：3.0000

==================================================
第三步：处理附件2数据 (15°入射角)
==================================================
✓ 成功读取文件：附件2.csv (编码: gbk)
  数据点数量：7469
  波数范围：399.67 - 4000.12 cm^-1
  反射率范围：0.00 - 102.74 %
执行传统方法分析...
  执行数据预处理...
  ✓ 插值完成：65536 个均匀数据点
  ✓ 波数步长：0.0549 cm^-1
  执行FFT分析...
  ✓ FFT分析完成
  ✓ 检测到主峰光程差：0.018331 cm
  ✓ 主峰幅度：13138.26
  计算外延层厚度...
  ✓ 入射角：15.0°
  ✓ 光程差：0.018331 cm
  ✓ 折射率：2.58
  ✓ 计算厚度：35.705 μm
执行数值优化分析...
  执行数值优化分析...
  初始光程差: 0.018331 cm
  估计厚度范围: 28.3 - 42.5 μm
    使用差分进化算法优化(d, A, p)参数...
  ✓ 优化完成:
    最优厚度: 28.624 μm
    消光系数参数A: 1.815943e-03
    消光系数参数p: 2.9962
    拟合R²: -1.082596
✓ 15°入射角处理完成
  传统方法厚度：35.705 μm
  优化方法厚度：28.624 μm
  消光系数参数A：1.815943e-03
  消光系数参数p：2.9962

==================================================
第四步：可靠性分析与效果对比
==================================================

==================================================
结果可靠性分析
==================================================
10°入射角计算厚度：35.605 μm
15°入射角计算厚度：35.705 μm
平均厚度：35.655 μm
绝对误差：0.099 μm
相对误差：0.28%
可靠性评估：优秀 (相对误差0.28%)

可靠性分析详情：
✓ 结果高度一致，算法精度优秀
✓ 两个入射角的测量结果相对误差<1%，远超工程精度要求
✓ 证明数学模型和FFT算法的正确性和稳定性

==================================================
算法效果对比分析
==================================================
传统方法结果：
  10°入射角：35.605 μm (固定折射率: 2.58)
  15°入射角：35.705 μm (固定折射率: 2.58)
  平均厚度：35.655 μm
  相对误差：0.279%

数值优化方法结果：
  10°入射角：28.545 μm (A: 1.81e-03, p: 3.000)
  15°入射角：28.624 μm (A: 1.82e-03, p: 2.996)
  平均厚度：28.584 μm
  相对误差：0.278%
  平均消光系数参数A：1.81e-03
  平均消光系数参数p：2.998

精度改进：0.3%
✓ 优化方法达到了很高的精度（相对误差 < 0.5%）

==================================================
第四步：生成结果报告与可视化
==================================================

生成附件1详细计算步骤图...
  生成详细计算步骤图表...
  ✓ 详细计算步骤图已保存：results\problem2_calculation_steps_10deg_28.5um.png/pdf

生成附件1详细分析图...
  ✓ 详细分析图已保存：results\problem2_detailed_analysis_10deg_28.5um.png/pdf

生成附件2详细计算步骤图...
  生成详细计算步骤图表...
  ✓ 详细计算步骤图已保存：results\problem2_calculation_steps_15deg_28.6um.png/pdf

生成附件2详细分析图...
  ✓ 详细分析图已保存：results\problem2_detailed_analysis_15deg_28.6um.png/pdf

生成综合结果对比图...

生成结果可视化图表...
✓ 结果图表已保存：
  PNG格式：results\problem2_results_summary_avg_35.7um.png    
  PDF格式：results\problem2_results_summary_avg_35.7um.pdf    
