# -*- coding: utf-8 -*-
"""
全参数优化算法 - 将所有关键物理参数视为未知自由变量

改进点：
1. 载流子浓度N作为优化变量
2. 阻尼常数gamma作为优化变量  
3. 有效质量m_eff作为优化变量（可选）
4. Sellmeier参数微调（可选）
5. 厚度d作为优化变量
"""

import numpy as np
from scipy.optimize import differential_evolution, minimize
from scipy.constants import c, e, epsilon_0, m_e
from scipy.fft import fft, fftfreq
from scipy.interpolate import interp1d


class FullParameterOptimizer:
    """全参数优化器 - 所有关键物理参数作为自由变量"""
    
    def __init__(self, material='SiC'):
        self.material = material.upper()
        
        # 设置参数的初始值和合理范围
        if self.material == 'SIC':
            self.param_bounds = {
                'thickness_um': (1.0, 100.0),           # 厚度范围
                'carrier_concentration': (1e14, 1e18),   # 载流子浓度范围
                'gamma': (1e12, 1e14),                   # 阻尼常数范围
                'm_eff_ratio': (0.5, 1.0),              # 有效质量比例范围
                'A': (6.0, 7.5),                        # Sellmeier参数A微调范围
                'B1': (1.5, 2.0),                       # Sellmeier参数B1微调范围
            }
            # 初始值
            self.initial_params = {
                'A': 6.7, 'B1': 1.73, 'C1': 0.256,
                'B2': 0.32, 'C2': 1250.0,
                'm_eff_base': 0.67 * m_e
            }
        elif self.material == 'SI':
            self.param_bounds = {
                'thickness_um': (1.0, 100.0),
                'carrier_concentration': (1e13, 1e17),
                'gamma': (1e11, 1e13),
                'm_eff_ratio': (0.2, 0.4),
                'A': (11.0, 12.5),
                'B1': (0.8, 1.1),
            }
            self.initial_params = {
                'A': 11.7, 'B1': 0.939, 'C1': 0.0513,
                'B2': 8.1e-3, 'C2': 1.16e6,
                'm_eff_base': 0.26 * m_e
            }
    
    def calculate_refractive_index(self, wavelength_um, params):
        """
        计算考虑所有参数的折射率
        
        Args:
            wavelength_um: 波长数组
            params: 优化参数字典
        """
        # 解包参数
        N = params['carrier_concentration']
        gamma = params['gamma']
        m_eff = self.initial_params['m_eff_base'] * params['m_eff_ratio']
        A = params['A']
        B1 = params['B1']
        
        # Sellmeier方程（使用优化的参数）
        lambda_sq = wavelength_um**2
        C1, B2, C2 = self.initial_params['C1'], self.initial_params['B2'], self.initial_params['C2']
        
        n_sq = A + (B1 * lambda_sq) / (lambda_sq - C1) + \
               (B2 * lambda_sq) / (lambda_sq - C2)
        n0 = np.sqrt(np.maximum(n_sq, 1.0))
        
        if N <= 0:
            return n0
        
        # Drude模型（使用优化的参数）
        omega = 2 * np.pi * c / (wavelength_um * 1e-6)
        N_m3 = N * 1e6  # cm⁻³ to m⁻³
        omega_p_sq = N_m3 * e**2 / (epsilon_0 * m_eff)
        omega_p = np.sqrt(omega_p_sq)
        
        epsilon_inf = n0**2
        epsilon_carrier = -omega_p**2 / (omega**2 + 1j*gamma*omega)
        epsilon_total = epsilon_inf + epsilon_carrier
        
        n_with_carriers = np.sqrt(np.real(epsilon_total))
        return np.maximum(n_with_carriers, 1.0)
    
    def calculate_theoretical_reflectance(self, wavenumber, params, angle_deg):
        """
        计算理论反射率
        
        Args:
            wavenumber: 波数数组
            params: 优化参数字典
            angle_deg: 入射角
        """
        wavelength_um = 1e4 / wavenumber
        d_cm = params['thickness_um'] * 1e-4
        angle_rad = np.deg2rad(angle_deg)
        
        # 计算折射率
        n = self.calculate_refractive_index(wavelength_um, params)
        n_avg = np.mean(n)  # 简化处理，也可以使用波长依赖
        
        # 计算光程差
        optical_path_difference = 2 * n_avg * d_cm * np.sqrt(1 - (np.sin(angle_rad) / n_avg)**2)
        
        # 计算相位差
        phase_difference = 2 * np.pi * optical_path_difference / (wavelength_um * 1e-4)
        
        # 简化的Fabry-Perot干涉模型
        # 可以进一步改进为更精确的多光束干涉模型
        r1 = (1 - n_avg) / (1 + n_avg)  # 空气-外延层界面反射系数
        r2 = (n_avg - 3.2) / (n_avg + 3.2)  # 外延层-衬底界面反射系数（假设衬底n=3.2）
        
        # Fabry-Perot公式
        numerator = r1 + r2 * np.exp(-2j * phase_difference)
        denominator = 1 + r1 * r2 * np.exp(-2j * phase_difference)
        r_total = numerator / denominator
        
        theoretical_reflectance = np.abs(r_total)**2 * 100
        return np.clip(theoretical_reflectance, 0, 100)
    
    def objective_function(self, optimization_params, wavenumber, measured_reflectance, angle_deg):
        """
        目标函数：最小化理论与实测反射率的差异
        
        Args:
            optimization_params: 优化参数数组 [thickness, N, gamma, m_eff_ratio, A, B1]
            wavenumber: 波数数组
            measured_reflectance: 实测反射率
            angle_deg: 入射角
        """
        try:
            # 将优化参数数组转换为字典
            params = {
                'thickness_um': optimization_params[0],
                'carrier_concentration': optimization_params[1],
                'gamma': optimization_params[2],
                'm_eff_ratio': optimization_params[3],
                'A': optimization_params[4],
                'B1': optimization_params[5]
            }
            
            # 计算理论反射率
            theoretical_reflectance = self.calculate_theoretical_reflectance(
                wavenumber, params, angle_deg)
            
            # 计算拟合误差（均方根误差）
            error = np.sqrt(np.mean((measured_reflectance - theoretical_reflectance)**2))
            
            # 添加物理约束惩罚
            penalty = 0
            if params['thickness_um'] <= 0:
                penalty += 1e6
            if params['carrier_concentration'] <= 0:
                penalty += 1e6
            if params['gamma'] <= 0:
                penalty += 1e6
            if params['m_eff_ratio'] <= 0:
                penalty += 1e6
            
            return error + penalty
            
        except Exception as e:
            return 1e6  # 返回大误差值
    
    def optimize_all_parameters(self, wavenumber, reflectance, angle_deg, 
                               use_fft_initial_guess=True):
        """
        全参数优化主函数
        
        Args:
            wavenumber: 波数数组
            reflectance: 反射率数组
            angle_deg: 入射角
            use_fft_initial_guess: 是否使用FFT结果作为厚度初始猜测
        """
        print(f"执行{self.material}材料全参数优化...")
        
        # 1. 如果使用FFT初始猜测，先进行FFT分析
        if use_fft_initial_guess:
            # 数据预处理
            uniform_wn, uniform_ref = self._preprocess_data(wavenumber, reflectance)
            opd_initial = self._calculate_opd_from_fft(uniform_wn, uniform_ref)
            
            # 基于FFT结果估计初始厚度
            n_guess = 2.6 if self.material == 'SIC' else 3.4
            angle_rad = np.deg2rad(angle_deg)
            thickness_initial = opd_initial / (2 * np.sqrt(n_guess**2 - np.sin(angle_rad)**2)) * 1e4
            
            # 调整厚度边界
            thickness_bounds = (thickness_initial * 0.8, thickness_initial * 1.2)
        else:
            thickness_bounds = self.param_bounds['thickness_um']
        
        # 2. 设置优化边界
        bounds = [
            thickness_bounds,                              # 厚度
            self.param_bounds['carrier_concentration'],    # 载流子浓度
            self.param_bounds['gamma'],                    # 阻尼常数
            self.param_bounds['m_eff_ratio'],             # 有效质量比例
            self.param_bounds['A'],                       # Sellmeier参数A
            self.param_bounds['B1'],                      # Sellmeier参数B1
        ]
        
        print(f"  优化参数边界:")
        param_names = ['厚度(μm)', '载流子浓度(cm⁻³)', '阻尼常数(rad/s)', 
                      '有效质量比例', 'Sellmeier-A', 'Sellmeier-B1']
        for i, (name, bound) in enumerate(zip(param_names, bounds)):
            print(f"    {name}: {bound[0]:.2e} - {bound[1]:.2e}")
        
        # 3. 差分进化全局优化
        print("  执行差分进化全局优化...")
        result = differential_evolution(
            self.objective_function,
            bounds,
            args=(wavenumber, reflectance, angle_deg),
            seed=42,
            maxiter=200,
            popsize=15,
            atol=1e-8,
            tol=1e-8,
            workers=1
        )
        
        # 4. 局部精化（可选）
        if result.success:
            print("  执行局部精化优化...")
            local_result = minimize(
                self.objective_function,
                result.x,
                args=(wavenumber, reflectance, angle_deg),
                method='L-BFGS-B',
                bounds=bounds,
                options={'ftol': 1e-10, 'gtol': 1e-10}
            )
            
            if local_result.success and local_result.fun < result.fun:
                result = local_result
                print("  局部优化改进了结果")
        
        # 5. 解析优化结果
        if result.success:
            optimized_params = {
                'thickness_um': result.x[0],
                'carrier_concentration': result.x[1],
                'gamma': result.x[2],
                'm_eff_ratio': result.x[3],
                'A': result.x[4],
                'B1': result.x[5],
                'fitting_error': result.fun,
                'optimization_success': True
            }
            
            print(f"  ✓ 全参数优化成功:")
            print(f"    厚度: {optimized_params['thickness_um']:.3f} μm")
            print(f"    载流子浓度: {optimized_params['carrier_concentration']:.2e} cm⁻³")
            print(f"    阻尼常数: {optimized_params['gamma']:.2e} rad/s")
            print(f"    有效质量比例: {optimized_params['m_eff_ratio']:.3f}")
            print(f"    Sellmeier-A: {optimized_params['A']:.3f}")
            print(f"    Sellmeier-B1: {optimized_params['B1']:.3f}")
            print(f"    拟合误差: {optimized_params['fitting_error']:.6f}")
            
        else:
            print(f"  ✗ 优化失败: {result.message}")
            optimized_params = {
                'optimization_success': False,
                'error_message': result.message
            }
        
        return optimized_params
    
    def _preprocess_data(self, wavenumber, reflectance, num_points=2**14):
        """数据预处理"""
        interp_func = interp1d(wavenumber, reflectance, kind='linear', 
                              bounds_error=False, fill_value='extrapolate')
        uniform_wavenumber = np.linspace(wavenumber.min(), wavenumber.max(), num_points)
        uniform_reflectance = interp_func(uniform_wavenumber)
        return uniform_wavenumber, uniform_reflectance
    
    def _calculate_opd_from_fft(self, uniform_wavenumber, uniform_reflectance):
        """FFT分析计算光程差"""
        N = len(uniform_wavenumber)
        wavenumber_step = uniform_wavenumber[1] - uniform_wavenumber[0]
        
        reflectance_centered = uniform_reflectance - np.mean(uniform_reflectance)
        reflectance_fft = fft(reflectance_centered)
        fft_magnitude = np.abs(reflectance_fft)
        
        opd_axis = fftfreq(N, d=wavenumber_step)
        positive_opd_axis = opd_axis[:N // 2]
        positive_fft_magnitude = fft_magnitude[:N // 2]
        
        start_idx = max(1, int(0.001 * N))
        peak_index = np.argmax(positive_fft_magnitude[start_idx:]) + start_idx
        opd_value = positive_opd_axis[peak_index]
        
        return opd_value


# 使用示例
def example_full_parameter_optimization():
    """全参数优化使用示例"""
    print("="*60)
    print("全参数优化算法示例")
    print("="*60)
    
    # 创建优化器
    optimizer = FullParameterOptimizer('SiC')
    
    # 模拟数据（实际使用时替换为真实数据）
    wavenumber = np.linspace(400, 1200, 500)
    # 模拟含噪声的干涉光谱
    reflectance = 50 + 20 * np.cos(0.015 * wavenumber) + np.random.normal(0, 1, len(wavenumber))
    
    # 执行全参数优化
    result = optimizer.optimize_all_parameters(wavenumber, reflectance, 10.0)
    
    if result['optimization_success']:
        print("\n全参数优化成功完成！")
        print("所有关键物理参数均作为自由变量进行了优化")
    else:
        print(f"\n优化失败: {result.get('error_message', '未知错误')}")
    
    return result


if __name__ == "__main__":
    # 运行示例
    result = example_full_parameter_optimization()
