# 碳化硅外延层厚度测定算法附录

## 目录

1. [算法总览](#算法总览)
2. [问题1核心算法](#问题1核心算法)
3. [问题2核心算法](#问题2核心算法)
4. [问题3核心算法](#问题3核心算法)
5. [函数索引](#函数索引)
6. [使用指南](#使用指南)

---

## 算法总览

本附录包含从问题1、2、3中提取的核心算法，主要用于碳化硅和硅外延层厚度的精确测定。算法基于红外干涉法原理，结合先进的数学模型和数值优化技术。

### 核心技术特点
- **波长依赖折射率模型**：Sellmeier方程 + Drude模型
- **载流子效应修正**：自动估计掺杂载流子浓度
- **FFT光程差分析**：高精度干涉条纹分析
- **多材料支持**：SiC和Si材料参数
- **数值优化算法**：差分进化 + 局部优化

---

## 问题1核心算法

### 1.1 折射率模型算法
**类名**: `RefractiveIndexModel`
**功能**: 实现波长依赖的折射率计算，考虑载流子效应
**核心方程**: n(λ, N) = √[n₀²(λ) - ωₚ²(N)/(ω² + γ²)]

**主要方法**:
- `intrinsic_refractive_index(wavelength_um)`: 计算本征折射率（Sellmeier方程）
- `plasma_frequency(carrier_concentration)`: 计算等离子体频率
- `refractive_index_with_carriers(wavelength_um, carrier_concentration)`: 计算含载流子效应的折射率

**输入参数**:
- `material`: 材料类型 ('SiC' 或 'Si')
- `wavelength_um`: 波长 (μm)
- `carrier_concentration`: 载流子浓度 (cm⁻³)

**输出**: 折射率数值

### 1.2 载流子浓度估计算法
**类名**: `CarrierConcentrationEstimator`
**功能**: 通过光谱形状自动估计掺杂载流子浓度
**原理**: 分析长波段光谱斜率特征

**主要方法**:
- `estimate_from_spectrum_shape(wavenumber, reflectance)`: 从光谱形状估计载流子浓度

**输入参数**:
- `wavenumber`: 波数数组 (cm⁻¹)
- `reflectance`: 反射率数组 (%)

**输出**: 载流子浓度 (cm⁻³)

### 1.3 优化厚度计算算法
**函数名**: `calculate_thickness_optimized`
**功能**: 基于波长依赖折射率模型的优化厚度计算
**优势**: 相比传统固定折射率方法，精度显著提升

**输入参数**:
- `wavenumber`: 波数数组
- `reflectance`: 反射率数组
- `opd`: 光程差值
- `theta_i_deg`: 入射角 (度)
- `material`: 材料类型

**输出**: 包含厚度、载流子浓度、折射率等信息的字典

---

## 问题2核心算法

### 2.1 数据预处理算法
**函数名**: `load_spectral_data`, `preprocess_and_interpolate`
**功能**: 加载光谱数据并进行预处理
**特点**: 支持多种编码格式，自动插值到均匀网格

**主要步骤**:
1. 多编码格式文件读取
2. 无效数据清理
3. 线性插值到均匀网格
4. 基线校正

### 2.2 FFT光程差分析算法
**函数名**: `calculate_opd_from_fft`
**功能**: 通过FFT从干涉光谱中提取光程差
**原理**: 将波数域信号转换到光程差域，识别主峰位置

**核心步骤**:
1. 去除直流分量
2. 执行FFT变换
3. 计算幅度谱
4. 主峰识别与光程差提取

**输入参数**:
- `uniform_wavenumber`: 均匀波数网格
- `uniform_reflectance`: 均匀反射率数据

**输出**: 光程差值、OPD轴、FFT幅度谱

### 2.3 传统厚度计算算法
**函数名**: `calculate_thickness_traditional`
**功能**: 基于固定折射率的传统厚度计算方法
**数学模型**: d = L / (2 × √(n₁² - sin²(θᵢ)))

**输入参数**:
- `opd`: 光程差 (cm)
- `n1`: 外延层折射率
- `theta_i_deg`: 入射角 (度)

**输出**: 外延层厚度 (μm)

### 2.4 可靠性分析算法
**函数名**: `analyze_reliability`
**功能**: 分析两个入射角计算结果的一致性
**评估标准**: 相对误差 < 1% (优秀), < 5% (良好), < 10% (可接受)

---

## 问题3核心算法

### 3.1 多光束干涉分析算法
**函数名**: `analyze_multibeam_interference_condition`
**功能**: 分析多光束干涉的必要条件
**判断依据**: 精细度 F = 4r₁r₂/(1-r₁r₂)² > 阈值

**输入参数**:
- `n1`: 外延层折射率
- `n2`: 衬底折射率
- `thickness_um`: 厚度 (μm)
- `wavelength_um`: 波长 (μm)

**输出**: 精细度、反射系数、多光束干涉判断结果

### 3.2 消光系数模型算法
**类名**: `ExtinctionCoefficientModel`
**功能**: 实现消光系数模型 κ = A × λᵖ
**应用**: 用于考虑材料吸收损耗的精确建模

### 3.3 数值优化算法
**函数名**: `numerical_optimization_thickness`
**功能**: 数值优化求解厚度参数
**算法**: 差分进化全局优化
**优化目标**: 最小化理论与实测反射率的差异

---

## 函数索引

### 数据处理类
- `load_spectral_data()` - 加载光谱数据文件
- `preprocess_and_interpolate()` - 数据预处理和插值

### 分析计算类
- `calculate_opd_from_fft()` - FFT光程差分析
- `calculate_thickness_traditional()` - 传统厚度计算
- `calculate_thickness_optimized()` - 优化厚度计算
- `analyze_reliability()` - 可靠性分析

### 模型类
- `RefractiveIndexModel` - 折射率模型
- `CarrierConcentrationEstimator` - 载流子浓度估计
- `ExtinctionCoefficientModel` - 消光系数模型

### 高级算法类
- `analyze_multibeam_interference_condition()` - 多光束干涉分析
- `numerical_optimization_thickness()` - 数值优化算法

---

## 使用指南

### 基本使用流程

1. **数据加载与预处理**
```python
wavenumber, reflectance = load_spectral_data('data.csv')
uniform_wn, uniform_ref = preprocess_and_interpolate(wavenumber, reflectance)
```

2. **FFT分析提取光程差**
```python
opd, opd_axis, fft_mag = calculate_opd_from_fft(uniform_wn, uniform_ref)
```

3. **厚度计算**
```python
# 传统方法
thickness_trad = calculate_thickness_traditional(opd, 2.58, 10.0)

# 优化方法
result_opt = calculate_thickness_optimized(wavenumber, reflectance, opd, 10.0, 'SiC')
```

4. **可靠性分析**
```python
reliability = analyze_reliability(thickness_10deg, thickness_15deg)
```

### 材料参数选择
- **SiC材料**: 使用 `material='SiC'`，适用于碳化硅外延层
- **Si材料**: 使用 `material='Si'`，适用于硅外延层

### 精度优化建议
1. 使用优化方法替代传统固定折射率方法
2. 进行双入射角交叉验证
3. 根据材料类型选择合适的参数
4. 注意多光束干涉条件的判断

### 常见问题解决
- **数据读取失败**: 检查文件编码格式
- **FFT峰值识别错误**: 调整分析波数范围
- **可靠性较差**: 检查数据质量和预处理参数
- **多光束干涉影响**: 使用专门的多光束干涉算法

---

**注意**: 本附录中的算法已经过实际数据验证，具有良好的精度和稳定性。在实际应用中，建议根据具体材料和测试条件调整相关参数。
