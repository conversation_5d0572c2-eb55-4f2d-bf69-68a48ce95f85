# -*- coding: utf-8 -*-
"""
碳化硅外延层厚度测定算法使用示例
演示如何使用core_algorithms.py中的核心算法

本示例包含：
1. 问题1算法使用示例（优化折射率模型）
2. 问题2算法使用示例（FFT厚度计算）
3. 问题3算法使用示例（多材料支持）
"""

import numpy as np
from core_algorithms import *

def example_problem1_optimized_method():
    """
    问题1算法使用示例：优化的折射率模型方法
    """
    print("="*60)
    print("问题1算法使用示例：优化折射率模型")
    print("="*60)
    
    # 1. 加载数据
    try:
        wavenumber, reflectance = load_spectral_data('data/附件1.csv')
        print(f"✓ 成功加载数据：{len(wavenumber)} 个数据点")
    except:
        print("✗ 数据加载失败，使用模拟数据")
        # 生成模拟数据用于演示
        wavenumber = np.linspace(400, 1200, 1000)
        reflectance = 50 + 10 * np.sin(0.01 * wavenumber) + np.random.normal(0, 1, len(wavenumber))
    
    # 2. 数据预处理
    uniform_wavenumber, uniform_reflectance = preprocess_and_interpolate(wavenumber, reflectance)
    print(f"✓ 数据预处理完成：{len(uniform_wavenumber)} 个均匀数据点")
    
    # 3. FFT分析提取光程差
    opd, opd_axis, fft_magnitude = calculate_opd_from_fft(uniform_wavenumber, uniform_reflectance)
    print(f"✓ FFT分析完成，检测到光程差：{opd:.6f} cm")
    
    # 4. 传统方法计算厚度
    thickness_traditional = calculate_thickness_traditional(opd, 2.58, 10.0)
    print(f"✓ 传统方法厚度：{thickness_traditional:.3f} μm")
    
    # 5. 优化方法计算厚度
    result_optimized = calculate_thickness_optimized(wavenumber, reflectance, opd, 10.0, 'SiC')
    print(f"✓ 优化方法厚度：{result_optimized['thickness_um']:.3f} μm")
    print(f"✓ 载流子浓度：{result_optimized['carrier_concentration']:.2e} cm⁻³")
    print(f"✓ 优化折射率：{result_optimized['optimized_n_avg']:.4f}")
    
    # 6. 对比分析
    improvement = abs(thickness_traditional - result_optimized['thickness_um']) / thickness_traditional * 100
    print(f"✓ 厚度差异：{improvement:.2f}%")
    
    return {
        'traditional': thickness_traditional,
        'optimized': result_optimized,
        'opd': opd
    }


def example_problem2_fft_algorithm():
    """
    问题2算法使用示例：FFT厚度计算算法
    """
    print("\n" + "="*60)
    print("问题2算法使用示例：FFT厚度计算算法")
    print("="*60)
    
    # 模拟两个入射角的数据
    angles = [10.0, 15.0]
    thicknesses = []
    
    for angle in angles:
        print(f"\n处理入射角 {angle}° 数据...")
        
        # 1. 生成模拟数据（实际使用时替换为真实数据加载）
        wavenumber = np.linspace(400, 1200, 800)
        # 模拟干涉条纹
        phase = 0.02 * wavenumber * angle / 10.0
        reflectance = 45 + 15 * np.cos(phase) + np.random.normal(0, 0.5, len(wavenumber))
        
        # 2. 数据预处理
        uniform_wavenumber, uniform_reflectance = preprocess_and_interpolate(wavenumber, reflectance)
        
        # 3. FFT分析
        opd, opd_axis, fft_magnitude = calculate_opd_from_fft(uniform_wavenumber, uniform_reflectance)
        
        # 4. 厚度计算
        thickness = calculate_thickness_traditional(opd, 2.58, angle)
        thicknesses.append(thickness)
        
        print(f"  光程差：{opd:.6f} cm")
        print(f"  计算厚度：{thickness:.3f} μm")
    
    # 5. 可靠性分析
    reliability = analyze_reliability(thicknesses[0], thicknesses[1])
    print(f"\n可靠性分析结果：")
    print(f"  平均厚度：{reliability['average']:.3f} μm")
    print(f"  相对误差：{reliability['relative_error']:.2f}%")
    print(f"  可靠性评估：{reliability['reliability']}")
    
    return reliability


def example_problem3_multibeam_analysis():
    """
    问题3算法使用示例：多光束干涉分析
    """
    print("\n" + "="*60)
    print("问题3算法使用示例：多光束干涉分析")
    print("="*60)
    
    # 1. 创建多材料折射率模型
    sic_model = RefractiveIndexModel('SiC')
    si_model = RefractiveIndexModel('Si')
    
    # 2. 计算不同材料的折射率
    wavelengths = np.array([10.0, 15.0, 20.0])  # μm
    carrier_concentration = 1e16  # cm⁻³
    
    print("折射率对比分析：")
    print("波长(μm)\tSiC折射率\tSi折射率")
    for wl in wavelengths:
        n_sic = sic_model.refractive_index_with_carriers(wl, carrier_concentration)
        n_si = si_model.refractive_index_with_carriers(wl, carrier_concentration)
        print(f"{wl:.1f}\t\t{n_sic:.4f}\t\t{n_si:.4f}")
    
    # 3. 多光束干涉条件分析
    print("\n多光束干涉条件分析：")
    materials = [('SiC', 2.6, 3.2), ('Si', 3.4, 3.4)]
    thickness_um = 30.0
    wavelength_um = 12.0
    
    for material, n1, n2 in materials:
        result = analyze_multibeam_interference_condition(n1, n2, thickness_um, wavelength_um)
        print(f"{material}材料：")
        print(f"  精细度：{result['finesse']:.4f}")
        print(f"  多光束干涉：{'是' if result['is_multibeam'] else '否'}")
    
    # 4. 载流子浓度估计示例
    print("\n载流子浓度估计示例：")
    
    # 模拟光谱数据
    wavenumber = np.linspace(600, 1200, 300)
    # SiC: 斜率较陡
    reflectance_sic = 50 - 0.01 * wavenumber + np.random.normal(0, 0.5, len(wavenumber))
    # Si: 斜率较缓
    reflectance_si = 45 - 0.003 * wavenumber + np.random.normal(0, 0.5, len(wavenumber))
    
    sic_estimator = CarrierConcentrationEstimator('SiC')
    si_estimator = CarrierConcentrationEstimator('Si')
    
    N_sic = sic_estimator.estimate_from_spectrum_shape(wavenumber, reflectance_sic)
    N_si = si_estimator.estimate_from_spectrum_shape(wavenumber, reflectance_si)
    
    print(f"SiC载流子浓度估计：{N_sic:.2e} cm⁻³")
    print(f"Si载流子浓度估计：{N_si:.2e} cm⁻³")
    
    return {
        'sic_carrier': N_sic,
        'si_carrier': N_si,
        'multibeam_analysis': result
    }


def example_extinction_coefficient_model():
    """
    消光系数模型使用示例
    """
    print("\n" + "="*60)
    print("消光系数模型使用示例")
    print("="*60)
    
    # 创建消光系数模型
    extinction_model = ExtinctionCoefficientModel()
    
    # 计算不同波长的消光系数
    wavelengths = np.linspace(8, 25, 10)
    A = 1e-4  # 消光系数参数A
    p = 1.5   # 消光系数参数p
    
    print("波长(μm)\t消光系数")
    for wl in wavelengths:
        extinction = extinction_model.calculate_extinction(wl, A, p)
        print(f"{wl:.1f}\t\t{extinction:.6f}")
    
    print(f"\n基础折射率：{extinction_model.get_base_refractive_index()}")
    
    return extinction_model


def example_numerical_optimization():
    """
    数值优化算法使用示例
    """
    print("\n" + "="*60)
    print("数值优化算法使用示例")
    print("="*60)
    
    # 生成模拟数据
    wavenumber = np.linspace(400, 1200, 200)
    # 模拟含噪声的干涉光谱
    true_thickness = 25.0  # μm
    reflectance = 50 + 20 * np.cos(0.015 * wavenumber) + np.random.normal(0, 1, len(wavenumber))
    
    # 设置优化边界
    bounds = [
        (20.0, 30.0),    # 厚度范围 (μm)
        (1e-5, 1e-3),    # 消光系数参数A
        (0.5, 2.0)       # 消光系数参数p
    ]
    
    print("执行数值优化...")
    try:
        result = numerical_optimization_thickness(wavenumber, reflectance, 10.0, bounds)
        
        if result.success:
            d_opt, A_opt, p_opt = result.x
            print(f"✓ 优化成功")
            print(f"  优化厚度：{d_opt:.3f} μm")
            print(f"  消光系数A：{A_opt:.6e}")
            print(f"  消光系数p：{p_opt:.4f}")
            print(f"  拟合误差：{result.fun:.6f}")
        else:
            print("✗ 优化失败")
            
    except Exception as e:
        print(f"✗ 优化过程出错：{e}")
    
    return result if 'result' in locals() else None


def main():
    """
    主函数：运行所有算法使用示例
    """
    print("碳化硅外延层厚度测定算法使用示例")
    print("="*80)
    
    # 运行各个示例
    try:
        result1 = example_problem1_optimized_method()
        result2 = example_problem2_fft_algorithm()
        result3 = example_problem3_multibeam_analysis()
        extinction_model = example_extinction_coefficient_model()
        optimization_result = example_numerical_optimization()
        
        # 总结
        print("\n" + "="*80)
        print("算法示例运行完成")
        print("="*80)
        print("✓ 所有核心算法已成功演示")
        print("✓ 算法库可以正常使用")
        print("✓ 建议根据实际数据调整参数")
        
        return {
            'problem1': result1,
            'problem2': result2,
            'problem3': result3,
            'extinction': extinction_model,
            'optimization': optimization_result
        }
        
    except Exception as e:
        print(f"✗ 示例运行出错：{e}")
        return None


if __name__ == "__main__":
    # 运行示例
    results = main()
